#include <stdio.h>
#include <stdlib.h>
#include <stdbool.h>
#include <string.h>
#include <dirent.h>
#include <time.h>
#include <unistd.h>
#include <signal.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/wait.h>
#include <glob.h>

#define MAX_LINE 256
#define SYS_PCI_DEVICES "/sys/bus/pci/devices/"
#define TIMESTAMP_FILE "timestamp"
#define REBOOTCOUNT_FILE "rebootcount"
#define INITIAL_PCI_DEVICES "initial_pci_devices.txt"
#define REBOOT_LOG "reboot.log"

volatile sig_atomic_t stop_flag = 0;

// SIGINT 信號處理函式
void handle_sigint(int sig);
int file_exists(const char *filename);
void write_timestamp(int hours);
time_t read_timestamp();
int update_rebootcount();
void log_initial_info(FILE *log_fp, int reboot_count);
char **fetch_pci_bdfs(int *count);
void free_pci_bdfs(char **bdfs, int count);
void execute_lspci(const char *bfd, const char *suffix);
void run_command_to_file(const char *command, const char *filename);
void compare_and_log(const char *init_file, const char *current_file, const char *bfd, FILE *log_fp);
int setup_systemd_service(int argc, char *argv[]);
void disable_selinux();

// SIGINT 信號處理函式
void handle_sigint(__attribute__((unused)) int sig) {
    stop_flag = 1;
}

// 檔案是否存在
int file_exists(const char *filename) {
    struct stat buffer;
    return (stat(filename, &buffer) == 0);
}

// 關閉 SELinux 的函數
void disable_selinux() {
    // 先檢查 SELinux 配置檔是否存在
    if (access("/etc/selinux/config", F_OK) != 0) {
        // 配置檔不存在，直接返回不做任何處理
        return;
    }

    FILE *selinux_file = fopen("/etc/selinux/config", "r+");
    if (!selinux_file) {
        // 如果打開失敗，也直接返回
        return;
    }

    char buffer[1024];
    long pos = 0;

    // 暫時關閉 SELinux
    system("setenforce 0");

    // 修改配置檔
    while (fgets(buffer, sizeof(buffer), selinux_file)) {
        if (strstr(buffer, "SELINUX=enforcing")) {
            pos = ftell(selinux_file) - strlen(buffer);
            fseek(selinux_file, pos, SEEK_SET);
            fputs("SELINUX=disabled\n", selinux_file);
            break;
        }
    }

    fclose(selinux_file);
}

// 寫入 timestamp
void write_timestamp(int hours) {
    FILE *fp = fopen(TIMESTAMP_FILE, "w");
    if (!fp) {
        perror("Failed to write timestamp file");
        exit(EXIT_FAILURE);
    }
    time_t now = time(NULL) + hours * 3600;
    fprintf(fp, "%ld\n", now);
    fclose(fp);
}

// 讀取 timestamp
time_t read_timestamp() {
    FILE *fp = fopen(TIMESTAMP_FILE, "r");
    if (!fp) {
        perror("Failed to read timestamp file");
        exit(EXIT_FAILURE);
    }
    time_t timestamp;
    fscanf(fp, "%ld", &timestamp);
    fclose(fp);
    return timestamp;
}

// 更新或初始化 rebootcount
int update_rebootcount() {
    int count = 1;

    // 嘗試打開文件
    FILE *fp = fopen(REBOOTCOUNT_FILE, "r");
    if (fp) {
        // 如果文件存在，讀取當前值
        fscanf(fp, "%d", &count);
        count++;  // 增加重啟次數
        fclose(fp);

        // 再次打開文件以寫入新值
        fp = fopen(REBOOTCOUNT_FILE, "w");
        if (!fp) {
            perror("Failed to update rebootcount file");
            exit(EXIT_FAILURE);
        }
        fprintf(fp, "%d\n", count);
        fclose(fp);
    } else {
        // 如果文件不存在，創建文件並初始化為 1
        fp = fopen(REBOOTCOUNT_FILE, "w");
        if (!fp) {
            perror("Failed to create rebootcount file");
            exit(EXIT_FAILURE);
        }
        fprintf(fp, "%d\n", count);
        fclose(fp);
    }

    return count;
}

// 記錄初始測試訊息
void log_initial_info(FILE *log_fp, int reboot_count) {
    char time_str[64];
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    strftime(time_str, sizeof(time_str), "%Y/%m/%d %H:%M:%S", tm_info);
    fprintf(log_fp, "\n\n%s #########Start to test#########\n", time_str);
    fprintf(log_fp, "\t\t\tReboot Count: %d\n", reboot_count);
    fflush(log_fp);
}

// 從 /sys/bus/pci/devices/ 提取 PCI BDF
char **fetch_pci_bdfs(int *count) {
    DIR *dir = opendir(SYS_PCI_DEVICES);
    if (!dir) {
        perror("Failed to open PCI devices directory");
        exit(EXIT_FAILURE);
    }
    struct dirent *entry;
    char **bdfs = NULL;
    *count = 0;
    while ((entry = readdir(dir)) != NULL) {
        if (entry->d_name[0] == '.') continue;
        bdfs = realloc(bdfs, (*count + 1) * sizeof(char *));
        bdfs[*count] = strdup(entry->d_name);
        (*count)++;
    }
    closedir(dir);
    return bdfs;
}

// 釋放 BDF 清單
void free_pci_bdfs(char **bdfs, int count) {
    for (int i = 0; i < count; i++) {
        free(bdfs[i]);
    }
    free(bdfs);
}

// 執行 lspci 測試
void execute_lspci(const char *bdf, const char *suffix) {
    char command[256], filename[256], full_command[512];
    
    // Safely construct command and filename
    snprintf(command, sizeof(command), "lspci -s %s -vv", bdf);
    snprintf(filename, sizeof(filename), "%s%s", bdf, suffix);
    
    // Check for potential truncation in full_command
    size_t ret = snprintf(full_command, sizeof(full_command), "%s > %s", command, filename);
    if (ret >= sizeof(full_command)) {
        fprintf(stderr, "Warning: Command truncated in execute_lspci()\n");
        return;
    }
    
    system(full_command);
}

int setup_systemd_service(int argc, char *argv[]) {
    const char *service_path = "/etc/systemd/system/lpot_reboot.service";
    const char *script_path = "/lpot/reboot.sh";
    FILE *service_file;
    FILE *script_file;

    // 檢查 systemd 服務是否已存在
    if (access(service_path, F_OK) != 0) {
        // 打開 systemd 服務檔案
        service_file = fopen(service_path, "w");
        if (!service_file) {
            perror("Failed to create systemd service file");
            return -1;
        }

        // 打開或創建腳本檔案
        script_file = fopen(script_path, "w");
        if (!script_file) {
            perror("Failed to create script file");
            fclose(service_file);
            return -1;
        }

        // 動態生成執行參數
        char exec_args[256] = "";
        for (int i = 1; i < argc; i++) {
            strcat(exec_args, argv[i]);
            strcat(exec_args, " ");
        }

        // 寫入腳本內容
        fprintf(script_file, "#!/bin/bash\n");
        fprintf(script_file, "lpot %s\n", exec_args);
        fclose(script_file);

        // 確保腳本有執行權限
        chmod(script_path, 0755);

        // 寫入 systemd 服務設置
        fprintf(service_file, "[Unit]\n");
        fprintf(service_file, "Description=The systemd setup file for PCIE check\n");
        fprintf(service_file, "After=graphical.target\n\n");

        fprintf(service_file, "[Service]\n");
        fprintf(service_file, "ExecStart=%s\n", script_path);
        fprintf(service_file, "Restart=no\n");
        fprintf(service_file, "User=root\n");
        fprintf(service_file, "Group=root\n");
        fprintf(service_file, "WorkingDirectory=/lpot\n\n");

        fprintf(service_file, "[Install]\n");
        fprintf(service_file, "WantedBy=graphical.target\n");

        fclose(service_file);

        // 檢查並關閉 SELinux
        disable_selinux();

        // 執行 systemctl daemon-reload
        pid_t pid = fork();
        if (pid == 0) {
            execl("/bin/systemctl", "systemctl", "daemon-reload", NULL);
            exit(1);
        } else if (pid > 0) {
            int status;
            waitpid(pid, &status, 0);
            if (WIFEXITED(status) && WEXITSTATUS(status) != 0) {
                fprintf(stderr, "Failed to reload systemd daemon\n");
                return -1;
            }
        } else {
            perror("Fork failed");
            return -1;
        }

        // 執行 systemctl enable
        pid = fork();
        if (pid == 0) {
            execl("/bin/systemctl", "systemctl", "enable", "lpot_reboot.service", NULL);
            exit(1);
        } else if (pid > 0) {
            int status;
            waitpid(pid, &status, 0);
            if (WIFEXITED(status) && WEXITSTATUS(status) != 0) {
                fprintf(stderr, "Failed to enable lpot_reboot service\n");
                return -1;
            }
        } else {
            perror("Fork failed");
            return -1;
        }
    }

    return 0;
}


// 檢查 init_file 中是否包含指定的 BDF
int init_file_contains_bdf(const char *target_bdf) {
    DIR *dir;
    struct dirent *entry;

    // 打開目錄
    dir = opendir("/lpot/");
    if (!dir) {
        perror("opendir failed");
        return 0; // 無法檢查，假設不包含
    }

    // 遍歷目錄中的所有檔案
    while ((entry = readdir(dir)) != NULL) {
        // 檢查檔名是否以 "_init.txt" 結尾
        char *suffix = strstr(entry->d_name, "_init.txt");
        if (suffix && suffix[9] == '\0') {
            // 計算 BDF 部分的長度
            size_t bdf_length = suffix - entry->d_name;
            
            // 比對目錄中的 BDF 是否等於目標 BDF
            if (strncmp(entry->d_name, target_bdf, bdf_length) == 0) {
                closedir(dir);
                return 0; // 找到對應的 BDF，表示目標 BDF 存在於系統中
            }
        }
    }

    closedir(dir);
    return 1; // 沒有找到對應的 BDF，表示目標 BDF 不存在於系統中
}

// 刪除所有當前的 <bdf>.txt 結果檔案，但保留 <bdf>_init.txt 和 initial_pci_devices.txt 文件
void cleanup_bdf_files() {
    DIR *dir;
    struct dirent *entry;
    char current_file[256];
    int files_removed = 0;

    // 打開目錄
    dir = opendir("/lpot/");
    if (!dir) {
        perror("opendir failed");
        return;
    }

    // 讀取目錄中的所有檔案
    while ((entry = readdir(dir)) != NULL) {
        // 檢查檔名是否結尾是 .txt 且不是 _init.txt 或 initial_pci_devices.txt
        if (strlen(entry->d_name) > 4 && strcmp(entry->d_name + strlen(entry->d_name) - 4, ".txt") == 0 &&
            strstr(entry->d_name, "_init") == NULL && strcmp(entry->d_name, "initial_pci_devices.txt") != 0) {

            snprintf(current_file, sizeof(current_file), "/lpot/%s", entry->d_name);

            if (file_exists(current_file)) {
                if (remove(current_file) == 0) {
                    files_removed++;
                } else {
                    perror("Failed to remove file");
                }
            }
        }
    }

    // 如果有刪除文件，立即退出
    if (files_removed > 0) {
        printf("Total %d files cleaned up.\n", files_removed);
    }

    closedir(dir);
}

// 停止 systemd 服務
int stop_systemd_service(const char *service_name) {
    char command[256];
    snprintf(command, sizeof(command), "systemctl stop %s", service_name);
    int ret = system(command);
    if (ret == -1) {
        perror("Failed to execute systemctl command");
        return -1;
    } else if (WEXITSTATUS(ret) != 0) {
        fprintf(stderr, "Systemctl stop command failed with status %d\n", WEXITSTATUS(ret));
        return -1;
    }
    return 0;
}

void show_help(const char *program_name) {
    printf("Usage: %s [OPTIONS]\n", program_name);
    printf("Version: 1.2.4");
    printf("Author: Nephom,Chiang");
    printf("OPTIONS:\n");
    printf("  -t <hours>   Setup runtime, default is 12 hours.\n");
    printf("  -d <secs>    Setup delay time for reboot, default is 300 seconds.\n");
    printf("  -s <secs>    Setup delay time for driver ready, default is 300 seconds.");
    printf("  -p           Set stop flag when Error occurred!\n");
    printf("  -h, --help   Show Help menu\n");
    printf("\nExample:\n");
    printf("  %s -t 24 -d 600    Run reboot during 24 hours and each reboot wait for 600 seconds\n", program_name);
}

void filter_lpotscan_errors(const char *error_log_path, FILE *log_fp) {
    FILE *errorLog = fopen(error_log_path, "r");
    if (!errorLog) {
        perror("Failed to open error log");
        return;
    }

    char buffer[MAX_LINE];
    int write_line = 0;

    while (fgets(buffer, sizeof(buffer), errorLog)) {
        // 避免 "No devices changed" 這類無意義訊息
        if (strstr(buffer, "No devices changed")) {
            continue;
        }

        // 只要是有 '|' 符號的行，代表是設備變更資訊
        char *bdf_start = strchr(buffer, '|');
        if (bdf_start) {
            fprintf(log_fp, "%s", buffer); // 直接寫入 log_fp
            write_line = 1;
        } else if (write_line) {
            // 如果前面有輸出過一行 BDF 資訊，則允許繼續輸出變更內容
            if (strstr(buffer, "Before") || strstr(buffer, "After") || strstr(buffer, "Differences")) {
                fprintf(log_fp, "%s", buffer);
            }
        }
    }

    fclose(errorLog);
}

int process_pci_devices(int bdf_count, char **bdfs, FILE *log_fp, int stopService) {
    int *results = malloc(bdf_count * sizeof(int));
    if (!results) {
        perror("Failed to allocate memory for results");
        return EXIT_FAILURE;
    }

    char **new_devices = malloc(bdf_count * sizeof(char*));
    char **removed_devices = malloc(bdf_count * sizeof(char*));
    int new_count = 0;
    int removed_count = 0;

    glob_t init_files;
    if (glob("*_init.txt", 0, NULL, &init_files) != 0) {
        fprintf(log_fp, "Error finding init files\n");
        free(results);
        free(new_devices);
        free(removed_devices);
        return EXIT_FAILURE;
    }

    for (int i = 0; i < bdf_count; i++) {
        char current_file[64];
        snprintf(current_file, sizeof(current_file), "%s.txt", bdfs[i]);
        execute_lspci(bdfs[i], ".txt");
    }

    for (size_t j = 0; j < init_files.gl_pathc; j++) {
        char *init_file = init_files.gl_pathv[j];
        char bdf[32];
        sscanf(init_file, "%[^_]_init.txt", bdf);
        char current_file[64];
        snprintf(current_file, sizeof(current_file), "%s.txt", bdf);
        FILE *fp = fopen(current_file, "r");
        if (!fp) {
            removed_devices[removed_count] = strdup(bdf);
            removed_count++;
            fprintf(log_fp, "REMOVED Device: %s\n", bdf);
        } else {
            fclose(fp);
        }
    }

    for (int i = 0; i < bdf_count; i++) {
        char current_file[64];
        snprintf(current_file, sizeof(current_file), "%s.txt", bdfs[i]);
        int found_in_init = 0;
        for (size_t j = 0; j < init_files.gl_pathc; j++) {
            char *init_file = init_files.gl_pathv[j];
            char bdf[32];
            sscanf(init_file, "%[^_]_init.txt", bdf);
            if (strcmp(bdf, bdfs[i]) == 0) {
                found_in_init = 1;
                break;
            }
        }
        if (!found_in_init) {
            new_devices[new_count] = strdup(bdfs[i]);
            new_count++;
            fprintf(log_fp, "NEW Device: %s\n", bdfs[i]);
        }
    }

    int all_unchanged = (new_count == 0 && removed_count == 0);
    int overall_success = 1;
    if (all_unchanged) {
        FILE *logFile = fopen("/lpot/reboot.log", "a");
        if (!logFile) {
            perror("Failed to open log file");
            return 1;
        }

        for (int i = 0; i < bdf_count; i++) {
            char init_file[64], current_file[64];
            snprintf(init_file, sizeof(init_file), "%s_init.txt", bdfs[i]);
            snprintf(current_file, sizeof(current_file), "%s.txt", bdfs[i]);
            char command[256];
            snprintf(command, sizeof(command), "lpotscan %s %s %s", init_file, current_file, stopService ? "true" : "false");
            int result = system(command);
            results[i] = result;
            //fprintf(log_fp, "BDF: %s, Result: %d\n", bdfs[i], result);
            if (result != 0) {
                overall_success = 0;
            }
        }

        char timeStr[64];
	time_t now = time(NULL);
	struct tm *t = localtime(&now);
	strftime(timeStr, sizeof(timeStr), "%Y/%m/%d %H:%M:%S", t);
	if (!overall_success) {
	    fprintf(logFile, "%s Had devices changed\n", timeStr);
	    fflush(logFile); // 確保寫入
	    filter_lpotscan_errors("/tmp/lpotscan.log", logFile);
	    fflush(logFile); // 再次確保變更內容寫入
	    if (stopService) {
	        fprintf(logFile, "%s You setting -p parameter, I will stop reboot test.\n", timeStr);
		fflush(logFile);
		exit(EXIT_FAILURE);
	    }
        } else {
            fprintf(logFile, "%s No devices changed\n", timeStr);
        }

        fclose(logFile);
    }

    for (int i = 0; i < new_count; i++) {
        free(new_devices[i]);
    }
    for (int i = 0; i < removed_count; i++) {
        free(removed_devices[i]);
    }
    free(new_devices);
    free(removed_devices);
    free(results);
    globfree(&init_files);

    return (all_unchanged) ? EXIT_SUCCESS : EXIT_FAILURE;
}

int main(int argc, char *argv[]) {
    int wait_hours = 12, wait_seconds = 300, standby_time = 300; bool stopService = false;
    int opt;

    while ((opt = getopt(argc, argv, "t:d:s:ph")) != -1) {
        switch (opt) {
            case 't': wait_hours = atoi(optarg); break;
            case 'd': wait_seconds = atoi(optarg); break;
            case 's': standby_time = atoi(optarg); break;
            case 'p':
                      stopService = true; break;
            case 'h':
                show_help(argv[0]);
                return EXIT_SUCCESS;
            default:
                fprintf(stderr, "Using %s -h check Help\n", argv[0]);
                return EXIT_FAILURE;
        }
    }

    if (!file_exists(TIMESTAMP_FILE)) {
        write_timestamp(wait_hours);
    } else {
        time_t current_time = time(NULL);
        time_t timestamp = read_timestamp();

        if (current_time >= timestamp) {
            char error_msg[256];

            // 取得當前時間並格式化
            time_t rawtime;
            struct tm *timeinfo;
            char timestamp_str[20];
            time(&rawtime);
            timeinfo = localtime(&rawtime);
            strftime(timestamp_str, sizeof(timestamp_str), "%Y/%m/%d %H:%M:%S", timeinfo);

            snprintf(error_msg, sizeof(error_msg), "%s Execution halted: timestamp expired.\n", timestamp_str);

            FILE *log_fp = fopen(REBOOT_LOG, "a");
            if (log_fp) {
                fprintf(log_fp, "%s", error_msg);
                fclose(log_fp);
            }

            system("find /lpot/ -type f -name '*.txt' ! -name 'initial_*.txt' -exec rm -f {} +");
	    system("configscan_log.sh");

            return EXIT_FAILURE;
        }
    }

    if (setup_systemd_service(argc, argv) != 0) {
        fprintf(stderr, "Failed to setup systemd service. Exiting.\n");
        return EXIT_FAILURE;
    }

    int reboot_count = update_rebootcount();
    FILE *log_fp = fopen(REBOOT_LOG, "a");
    if (!log_fp) {
        perror("Failed to open reboot.log");
        return EXIT_FAILURE;
    }

    // 取得當前時間並格式化
    time_t rawtime;
    struct tm *timeinfo;
    char timestamp_str[20];
    time(&rawtime);
    timeinfo = localtime(&rawtime);
    strftime(timestamp_str, sizeof(timestamp_str), "%Y/%m/%d %H:%M:%S", timeinfo);
    log_initial_info(log_fp, reboot_count);

    // 等候 standby_time 秒
    fprintf(log_fp, "%s Wait %d seconds for devices driver ready. \n", timestamp_str, standby_time);
    fflush(log_fp);  // 確保 log 立即寫入
    printf("%s Wait %d seconds for devices driver ready. \n", timestamp_str, standby_time);
    fflush(stdout);
    sleep(standby_time);

    int bdf_count;
    char **bdfs = fetch_pci_bdfs(&bdf_count);

    if (!file_exists(INITIAL_PCI_DEVICES)) {
        char full_command[512];
        snprintf(full_command, sizeof(full_command), "lspci -vv > %s", INITIAL_PCI_DEVICES);
        system(full_command);
        for (int i = 0; i < bdf_count; i++) {
            execute_lspci(bdfs[i], "_init.txt");
        }
    }

    // 取得當前時間並格式化
    time(&rawtime);
    timeinfo = localtime(&rawtime);
    strftime(timestamp_str, sizeof(timestamp_str), "%Y/%m/%d %H:%M:%S", timeinfo);
    fprintf(log_fp, "%s Analyzing\n", timestamp_str);
    fflush(log_fp);  // 確保 log 立即寫入
    fprintf(log_fp, "%s Scan Config space...\n", timestamp_str);
    fflush(log_fp);  // 確保 log 立即寫入
    system("configscan");

    // 取得當前時間並格式化
    time(&rawtime);
    timeinfo = localtime(&rawtime);
    strftime(timestamp_str, sizeof(timestamp_str), "%Y/%m/%d %H:%M:%S", timeinfo);

    fprintf(log_fp, "%s Scan Done.\n", timestamp_str);

    int result = process_pci_devices(bdf_count, bdfs, log_fp, stopService);
    if (result != EXIT_SUCCESS) {
        // 取得當前時間並格式化
        time(&rawtime);
        timeinfo = localtime(&rawtime);
        strftime(timestamp_str, sizeof(timestamp_str), "%Y/%m/%d %H:%M:%S", timeinfo);

        fprintf(log_fp, "%s PCI devices check failed\n", timestamp_str);
        fclose(log_fp);
        free_pci_bdfs(bdfs, bdf_count);
        return EXIT_FAILURE;
    }

    cleanup_bdf_files();

    time(&rawtime);
    timeinfo = localtime(&rawtime);
    strftime(timestamp_str, sizeof(timestamp_str), "%Y/%m/%d %H:%M:%S", timeinfo);
    fprintf(log_fp, "%s Wait %d seconds for reboot SUT. \n", timestamp_str, wait_seconds);
    fflush(log_fp);  // 確保 log 立即寫入
    fsync(fileno(log_fp));  // 強制寫入磁碟
    sleep(wait_seconds);
    if (remove("/tmp/lpotscan.log") == 0) {
        printf("File deleted successfully\n");
    } else {
        printf("Error deleting file\n");
    }
    system("reboot");
    return 0;
}

